import { NextResponse } from 'next/server';
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';

const api = new WooCommerceRestApi({
    url: process.env.NEXT_PUBLIC_WORDPRESS_URL || '',
    consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY || '',
    consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET || '',
    version: 'wc/v3'
});

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const { id } = params;
        console.log('Fetching product with ID:', id);

        // Check if WooCommerce is configured
        if (!process.env.NEXT_PUBLIC_WORDPRESS_URL || !process.env.WOOCOMMERCE_CONSUMER_KEY || !process.env.WOOCOMMERCE_CONSUMER_SECRET) {
            console.log('WooCommerce not configured, returning mock product data for ID:', id);

            // Return mock product data based on common greenhouse film products
            const mockProduct = {
                id: parseInt(id),
                name: 'Greenhouse Film Product',
                slug: 'greenhouse-film-product',
                description: 'High-quality greenhouse film for optimal plant growth.',
                short_description: 'Professional greenhouse covering material.',
                price: '299.99',
                regular_price: '299.99',
                images: [
                    {
                        id: 1,
                        src: '/Home/greenhouse.webp',
                        alt: 'Greenhouse Film'
                    }
                ],
                categories: [
                    {
                        id: 1,
                        name: 'Greenhouse Films',
                        slug: 'greenhouse-films'
                    }
                ],
                status: 'publish',
                stock_status: 'instock'
            };

            return NextResponse.json({
                success: true,
                product: mockProduct,
                message: 'Using mock data - WooCommerce not configured'
            });
        }

        // Fetch the product by ID
        const response = await api.get(`products/${id}`);
        console.log('WooCommerce API Response:', {
            status: response.status,
            hasData: !!response.data
        });

        if (!response.data) {
            console.log('No product found with ID:', id);
            return NextResponse.json({
                success: false,
                message: 'Product not found'
            }, { status: 404 });
        }

        return NextResponse.json({
            success: true,
            product: response.data
        });

    } catch (error: any) {
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data,
            stack: error.stack
        });

        return NextResponse.json({
            success: false,
            message: error.message || 'Failed to fetch product',
            details: error.response?.data || 'No additional details available'
        }, { status: error.response?.status || 500 });
    }
}