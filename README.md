# Greenhouse Film Website

A modern website for Greenhouse Film, built with Next.js and featuring an AI-powered chatbot.

## Features

- Responsive design
- AI-powered chatbot for customer support
- Product catalog
- Contact form
- Modern UI with Tailwind CSS

## Getting Started

### Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- OpenAI API key

### Installation

1. Clone the repository:
```bash
git clone https://github.com/A0786A/greenhouse-film.git
cd greenhouse-film
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Set up environment variables:
   - Copy `.env.example` to `.env.local`
   - Add your OpenAI API key to `.env.local`

4. Run the development server:
```bash
npm run dev
# or
yarn dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
OPENAI_API_KEY=your-api-key-here
```

## Project Structure

- `/src/app` - Next.js app router pages
- `/src/components` - React components
- `/public` - Static assets
- `/src/app/api` - API routes

## Technologies Used

- Next.js 14
- React
- TypeScript
- Tailwind CSS
- OpenAI API

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

Your Name - [@yourtwitter](https://twitter.com/yourtwitter) - <EMAIL>

Project Link: [https://github.com/yourusername/greenhouse-film](https://github.com/yourusername/greenhouse-film)
